#!/usr/bin/env python3
"""
<PERSON>ript to fix SerializationException tests in Ktor API client test files.
These tests should expect SerializationError instead of ServerError.
"""

import os
import re

# Files to fix
test_files = [
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorGroupApiClientTest.kt",
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorModelApiClientTest.kt",
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorProviderApiClientTest.kt", 
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorSessionApiClientTest.kt",
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorSettingsApiClientTest.kt"
]

def fix_serialization_tests(filepath):
    """Fix SerializationException tests in a single file"""
    print(f"Fixing SerializationException tests in {filepath}...")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find and replace the pattern for SerializationException tests
    # Look for tests that have "SerializationException" in the name and fix the error handling
    lines = content.split('\n')
    new_lines = []
    in_serialization_test = False
    
    for i, line in enumerate(lines):
        # Check if we're entering a SerializationException test
        if 'fun `' in line and 'SerializationException' in line:
            in_serialization_test = True
            new_lines.append(line)
            continue
        
        # Check if we're leaving the test (next test function or end of class)
        if in_serialization_test and (line.strip().startswith('fun ') or line.strip().startswith('}')):
            if not line.strip().startswith('fun `') or 'SerializationException' not in line:
                in_serialization_test = False
        
        # Fix the error handling in SerializationException tests
        if in_serialization_test:
            if 'val error = result.value as ApiResourceError.ServerError' in line:
                indent = line[:len(line) - len(line.lstrip())]
                new_lines.append(f'{indent}val error = result.value as ApiResourceError.SerializationError')
                continue
            elif 'assertEquals(500, error.apiError.statusCode)' in line:
                # Skip this line - SerializationError doesn't have statusCode
                continue
            elif 'assertEquals(CommonApiErrorCodes.INTERNAL.code, error.apiError.code)' in line:
                # Skip this line - SerializationError doesn't have code
                continue
            elif 'assertTrue(error.apiError.message.contains("Data Serialization/Deserialization Error"))' in line:
                indent = line[:len(line) - len(line.lstrip())]
                new_lines.append(f'{indent}assertTrue(error.message.contains("Serialization Error"))')
                new_lines.append(f'{indent}assertTrue(error.description.contains("Failed to parse API response"))')
                continue
        
        new_lines.append(line)
    
    # Write the fixed content back
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"Fixed SerializationException tests in {filepath}")

def main():
    """Main function"""
    for filepath in test_files:
        if os.path.exists(filepath):
            fix_serialization_tests(filepath)
        else:
            print(f"File not found: {filepath}")
    
    print("All SerializationException tests fixed!")

if __name__ == "__main__":
    main()
