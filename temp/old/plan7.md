## **Refactoring Plan: Migrating to a Fully Reactive Repository Architecture**

### **1. Objective**

This document outlines the plan to refactor our application's data and state management layers. The primary goal is to evolve our architecture to a fully reactive model where repositories serve as the single source of truth for all data, and the UI state (`ChatState`) becomes a pure, declarative "view" on top of this data.

This will eliminate state duplication, prevent data inconsistencies, simplify our ViewModels and Use Cases, and make the entire system more robust and easier to test.

### **2. Core Concepts of the New Architecture**

The new design is governed by three simple but powerful principles:

1.  **Repositories are the Single Source of Truth:**
    *   Each repository (`SessionRepository`, `ModelRepository`, `SettingsRepository`) is responsible for fetching, caching, and exposing its data.
    *   They provide reactive `StateFlow`s for both collections of items (e.g., `models`) and for **individual items** (e.g., `getModelFlow(id)`). This is the key enabler for this refactor.

2.  **State is Observed, Not Duplicated:**
    *   The `ChatState` class will no longer hold its own copy of session data, models, or settings.
    *   Instead, it will observe an `activeSessionId` and use reactive operators (`flatMapLatest`, `combine`) to derive the complete UI state (`sessionDataState`, `displayedMessages`) directly from the repository flows. It becomes a pure state combiner with **no side effects**.

3.  **Use Cases Orchestrate Actions, Not State:**
    *   Use Cases trigger actions. For example, `LoadSessionUseCase`'s job is to call the appropriate `load...Details()` methods on the repositories.
    *   Use Cases **do not** directly manipulate the UI state (e.g., `state.setSessionDataSuccess(...)`). They trigger the data fetch, and the reactive system automatically updates the UI state through `ChatState`'s observations.

### **3. Step-by-Step Refactoring Guide**

#### **Step 1: Upgrade Repositories to be Fully Reactive (Foundation)**

The `SessionRepository` has already been updated to this pattern. We must now apply the same pattern to `ModelRepository` and `SettingsRepository`.

**Action Items:**

1.  **Update `ModelRepository` and `SettingsRepository` interfaces:**
    *   Add a `suspend fun getModelFlow(modelId: Long): StateFlow<DataState<RepositoryError, LLMModel>>`.
    *   Add a `suspend fun loadModelDetails(modelId: Long): Either<RepositoryError, LLMModel>`.
    *   Apply the same pattern for `SettingsRepository` with `getSettingsFlow` and `loadSettingsDetails`.

2.  **Implement the new pattern in `DefaultModelRepository` and `DefaultSettingsRepository`:**
    *   Just like in `DefaultSessionRepository`, add an internal `Mutex` and a `MutableMap<Long, MutableStateFlow<...>>` to cache the state of individual items.
    *   Implement `getModelFlow` to retrieve or create a `MutableStateFlow` from the map within a `mutex.withLock`.
    *   Implement `loadModelDetails` to call the API and update the specific `MutableStateFlow` in the cache with `Loading`, `Success`, or `Error` states.
    *   Ensure that CRUD operations (`add`, `update`, `delete`) also update the individual item cache alongside the main list.

*(The final code for these repositories from our previous discussion should be used here as the reference implementation.)*

---

#### **Step 2: Consolidate and Refactor the `ChatState` Layer**

We will simplify our state management interfaces into a single, powerful `ChatState` and move all state derivation logic into `ChatStateImpl`.

**Action Items:**

1.  **Delete `SessionState.kt` and `InteractionState.kt`:** Their responsibilities will be merged into a single `ChatState` interface.

2.  **Update the `ChatState.kt` interface:**
    *   It will now define all state properties (`sessionDataState`, `displayedMessages`, `inputContent`, etc.) and mutation methods (`setInputContent`, `setActiveSessionId`, etc.).

3.  **Heavily refactor `ChatStateImpl.kt`:**
    *   **Change Constructor:** It will no longer take `ThreadBuilder` and `Clock` directly (unless needed for other logic). Instead, its new dependencies will be the **reactive repositories** and a `CoroutineScope`:
        ```kotlin
        class ChatStateImpl(
            private val sessionRepository: SessionRepository,
            private val settingsRepository: SettingsRepository,
            private val modelRepository: ModelRepository,
            private val threadBuilder: ThreadBuilder,
            private val backgroundScope: CoroutineScope
        ) : ChatState { ... }
        ```
    *   **Remove Direct State Setters:** Delete methods like `setSessionDataLoading`, `setSessionDataSuccess`, `updateSessionMessages`, etc. The `sessionDataState` will be *derived*, not *set*.
    *   **Implement Derived State:** Create the `sessionDataState` by reactively combining flows from the repositories, driven by a new internal `_activeSessionId` flow. This implementation should be **side-effect free**.

    **Example of the new `sessionDataState` derivation in `ChatStateImpl`:**
    ```kotlin
    // Inside ChatStateImpl

    private val _activeSessionId = MutableStateFlow<Long?>(null)

    // Intermediate flow for the active session
    private val activeChatSessionState: StateFlow<DataState<...>> =
        _activeSessionId.flatMapLatest { id ->
            if (id == null) flowOf(DataState.Success(null))
            else sessionRepository.getSessionFlow(id) // Observe the session
        }.stateIn(...)

    // Intermediate flow for the active model
    private val llmModelForActiveSession: StateFlow<DataState<...>> =
        activeChatSessionState.flatMapLatest { sessionState ->
            val modelId = sessionState.dataOrNull?.currentModelId
            if (modelId == null) flowOf(DataState.Success(null))
            else modelRepository.getModelFlow(modelId) // Observe the model
        }.stateIn(...)

    // ... similar flow for settings ...

    // Final combined state
    override val sessionDataState: StateFlow<DataState<...>> =
        combine(
            activeChatSessionState,
            llmModelForActiveSession,
            modelSettingsForActiveSession
        ) { /* pure combination logic */ }
        .stateIn(...)
    ```

---

#### **Step 3: Refactor Use Cases to Orchestrate Actions**

Use Cases will now trigger repository actions and update the simple `activeSessionId` state, trusting the reactive system to handle the rest.

**Action Items:**

1.  **Refactor `LoadSessionUseCase`:**
    *   **Responsibility Change:** Its `execute` method will no longer call `state.setSessionDataLoading/Success/Error`.
    *   **New Logic:**
        1.  Set the `state.activeSessionId(sessionId)`.
        2.  Call `sessionRepository.loadSessionDetails(sessionId)`.
        3.  On success, inspect the returned `ChatSession` for `currentModelId` and `currentSettingsId`.
        4.  Asynchronously trigger `modelRepository.loadModelDetails(...)` and `settingsRepository.loadSettingsDetails(...)`.
    *   The `loadLLMModel` and `loadModelSettings` helper methods inside the use case are now redundant for state derivation but can be kept if other use cases need them for one-off fetches.

2.  **Refactor `SelectModelUseCase`, `SelectSettingsUseCase`, `DeleteMessageUseCase`, etc.:**
    *   **Remove Manual State Updates:** These use cases must remove all calls to `state.setSessionDataSuccess`, `state.updateSessionModelId`, etc.
    *   **New Logic:**
        1.  Get the `activeSessionId` from `state.activeSessionId.value`.
        2.  Call the relevant repository method (e.g., `sessionRepository.updateSessionModel(...)`).
        3.  **That's it.** The repository will update its internal cache, the `StateFlow` for that item will emit the new value, and `ChatStateImpl` will automatically recompose the `sessionDataState`.

    **Example: `SelectModelUseCase` Refactor**
    ```kotlin
    // BEFORE
    suspend fun execute(modelId: Long?) {
        // ... calls sessionRepository.updateSessionModel ...
        // On success:
        state.updateSessionModelId(modelId) // MANUALLY UPDATE STATE
        loadModelIntoSessionData(modelId)    // MANUALLY FETCH & UPDATE STATE
    }

    // AFTER
    suspend fun execute(modelId: Long?) {
        val sessionId = state.activeSessionId.value ?: return
        // Just call the repository. The reactive chain will handle the rest.
        sessionRepository.updateSessionModel(
            sessionId = sessionId,
            request = UpdateSessionModelRequest(modelId = modelId)
        ).mapLeft { /* handle error */ }
    }
    ```

---

#### **Step 4: Simplify `ChatViewModel`**

With the logic moved to `ChatStateImpl` and Use Cases, the ViewModel becomes a very thin pass-through layer.

**Action Items:**

1.  **Confirm Dependencies:** The ViewModel's constructor will no longer need direct access to all repositories if they are only used within `ChatStateImpl` or Use Cases. It will primarily depend on `ChatState` and the Use Cases.
2.  **Expose State:** The ViewModel will simply expose the `StateFlow`s from the `ChatState` interface.
    ```kotlin
    val sessionDataState: StateFlow<...> = state.sessionDataState
    val displayedMessages: StateFlow<...> = state.displayedMessages
    ```
3.  **Delegate Actions:** The action methods (`loadSession`, `selectModel`, etc.) will continue to launch coroutines and call the refactored Use Cases. Some methods may need to be updated to pass the `activeSessionId` to their respective use cases if required.

---

### **4. Summary of Benefits**

*   **Single Source of Truth:** Data for sessions, models, and settings will live in one and only one place: their respective repositories. No more state drift.
*   **Improved Data Consistency:** Updating an item in one place (e.g., changing a session name) will automatically and instantly reflect everywhere that item is observed.
*   **Simplified State Management:** `ChatStateImpl` becomes a declarative and predictable state computer. Use Cases become simple action dispatchers. The ViewModel is trivial.
*   **Enhanced Testability:** It's much easier to test a pure state combiner like `ChatStateImpl` by providing mock repository flows. It's also easier to test Use Cases that only have a single responsibility.
*   **Robustness:** The architecture is less prone to bugs caused by forgetting to manually update a piece of state.

This refactor is a significant investment in our codebase's health and will pay dividends in future development speed and stability.