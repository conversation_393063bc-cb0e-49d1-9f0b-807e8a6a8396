Of course. Here is a detailed, phased development plan for creating a Compose UI for the `SettingsConfigViewModel`.

This plan breaks down the task into logical, manageable phases, starting from the foundational UI structure and progressively adding features like data display, editing, adding, and deleting. It follows the architectural patterns and styling established by the existing `ModelsTab` and `ProvidersTab` components.

---

### **Development Plan: Compose UI for `SettingsConfigViewModel`**

The goal is to create a fully functional UI for managing `ModelSettings` profiles, matching the logic provided by `SettingsConfigViewModel`. The UI will feature a master-detail layout.

*   **Master Pane:** Allows selecting an `LLMModel` and displays a list of its associated settings profiles.
*   **Detail Pane:** Displays the details of a selected settings profile.
*   **Dialogs:** For adding, editing, and deleting settings profiles.

---

### **Phase 1: Scaffolding and Foundation**

In this phase, we'll create the necessary files and set up the basic structure of the `SettingsConfigTab`, integrating it into the existing `SettingsScreen`.

**1.1. Create New Files:**

Create the following new Kotlin files in `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/`:

*   `SettingsConfigTab.kt`: The main composable for the tab, analogous to `ModelsTab.kt`.
*   `SettingsListPanel.kt`: The master pane (left side) for model selection and settings list.
*   `SettingsDetailPanel.kt`: The detail pane (right side) for showing selected settings info.
*   `SettingsDialogs.kt`: A router composable to manage Add/Edit/Delete dialogs, similar to `ModelsDialogs.kt`.
*   `SettingsFormDialog.kt`: The dialog containing the form for adding or editing a settings profile.

**1.2. Define the Actions Interface:**

In `SettingsActions.kt`, expand the `SettingsConfigTabActions` interface to include all necessary user interactions.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsActions.kt

interface SettingsConfigTabActions {
    fun onLoadModelsAndSettings()
    fun onSelectModel(model: LLMModel?)
    fun onSelectSettings(settings: ModelSettings?)
    fun onStartAddingNewSettings()
    fun onStartEditingSettings(settings: ModelSettings)
    fun onStartDeletingSettings(settings: ModelSettings)
    fun onUpdateSettingsForm(update: (SettingsFormState) -> SettingsFormState)
    fun onSaveSettings()
    fun onDeleteSettings(settingsId: Long)
    fun onCancelDialog()
}
```

**1.3. Update the Main Tab Composable (`SettingsConfigTab.kt`):**

Replace the placeholder content in `SettingsConfigTab.kt` with a master-detail layout that properly handles the `DataState` from the ViewModel.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsConfigTab.kt

@Composable
fun SettingsConfigTab(
    state: SettingsConfigTabState,
    actions: SettingsConfigTabActions,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.fillMaxSize()) {
        when (val modelsState = state.modelsUiState) {
            is DataState.Loading -> LoadingStateDisplay("Loading models and settings...")
            is DataState.Error -> ErrorStateDisplay(
                "Failed to load configuration",
                modelsState.error,
                onRetry = { actions.onLoadModelsAndSettings() }
            )
            is DataState.Success -> {
                Row(modifier = Modifier.fillMaxSize()) {
                    // Master Panel (Left)
                    SettingsListPanel(
                        models = modelsState.data,
                        selectedModel = state.selectedModel,
                        settingsList = state.settingsListForSelectedModel ?: emptyList(),
                        selectedSettings = state.selectedSettings,
                        onModelSelected = actions::onSelectModel,
                        onSettingsSelected = actions::onSelectSettings,
                        onAddNewSettings = actions::onStartAddingNewSettings,
                        modifier = Modifier.weight(1f).fillMaxHeight()
                    )
                    // Detail Panel (Right)
                    SettingsDetailPanel(
                        settings = state.selectedSettings,
                        onEdit = actions::onStartEditingSettings,
                        onDelete = actions::onStartDeletingSettings,
                        modifier = Modifier.weight(1f).fillMaxHeight().padding(start = 16.dp)
                    )
                }
            }
            is DataState.Idle -> {
                Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize()) {
                    Button(onClick = { actions.onLoadModelsAndSettings() }) {
                        Text("Load Settings Configuration")
                    }
                }
            }
        }

        // Dialogs will be handled here, based on state.dialogState
        SettingsDialogs(
            dialogState = state.dialogState,
            actions = actions
        )
    }
}
```

---

### **Phase 2: Building the Master & Detail Panels (Read-Only)**

This phase focuses on displaying the data from the ViewModel in the master-detail layout.

**2.1. Implement `SettingsListPanel.kt` (Master Pane):**

This panel will contain a dropdown to select a model and a list to show the settings for that model.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsListPanel.kt
import eu.torvian.chatbot.app.compose.common.ConfigDropdown
// ... other imports

@Composable
fun SettingsListPanel(
    models: List<LLMModel>,
    selectedModel: LLMModel?,
    settingsList: List<ModelSettings>,
    selectedSettings: ModelSettings?,
    onModelSelected: (LLMModel?) -> Unit,
    onSettingsSelected: (ModelSettings) -> Unit,
    onAddNewSettings: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        Column(modifier = Modifier.fillMaxSize().padding(16.dp)) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("Settings Profiles", style = MaterialTheme.typography.headlineSmall)
                if (selectedModel != null) {
                    FloatingActionButton(
                        onClick = onAddNewSettings,
                        modifier = Modifier.size(40.dp)
                    ) {
                        Icon(Icons.Default.Add, "Add new settings profile")
                    }
                }
            }
            Spacer(modifier = Modifier.height(16.dp))

            // Model Selector
            ConfigDropdown(
                label = "Select Model",
                items = models,
                selectedItem = selectedModel,
                onItemSelected = onModelSelected,
                itemText = { it.displayName ?: it.name }
            )
            Divider(modifier = Modifier.padding(vertical = 16.dp))

            // Settings List
            if (selectedModel == null) {
                Text("Select a model to see its settings profiles.", textAlign = TextAlign.Center)
            } else if (settingsList.isEmpty()) {
                Text("No settings profiles found for this model.", textAlign = TextAlign.Center)
            } else {
                LazyColumn(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                    items(settingsList) { settings ->
                        SettingsListItem(
                            settings = settings,
                            isSelected = selectedSettings?.id == settings.id,
                            onClick = { onSettingsSelected(settings) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SettingsListItem(
    settings: ModelSettings,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth().clickable(onClick = onClick),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) MaterialTheme.colorScheme.primaryContainer
            else MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(modifier = Modifier.padding(12.dp), verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = settings.name,
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.weight(1f)
            )
            Surface(
                color = MaterialTheme.colorScheme.secondaryContainer,
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = settings.modelType.name,
                    style = MaterialTheme.typography.labelSmall,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
                )
            }
        }
    }
}
```

**2.2. Implement `SettingsDetailPanel.kt` (Detail Pane):**

This panel will show the details of the currently selected `ModelSettings`. It needs to handle different settings types.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsDetailPanel.kt

@Composable
fun SettingsDetailPanel(
    settings: ModelSettings?,
    onEdit: (ModelSettings) -> Unit,
    onDelete: (ModelSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier) {
        if (settings == null) {
            Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize()) {
                Text("Select a settings profile to view details.")
            }
        } else {
            Column(modifier = Modifier.fillMaxSize().padding(16.dp)) {
                // Header with actions
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(settings.name, style = MaterialTheme.typography.headlineSmall)
                    Row {
                        IconButton(onClick = { onEdit(settings) }) {
                            Icon(Icons.Default.Edit, "Edit Settings")
                        }
                        IconButton(onClick = { onDelete(settings) }) {
                            Icon(Icons.Default.Delete, "Delete Settings", tint = MaterialTheme.colorScheme.error)
                        }
                    }
                }
                Spacer(modifier = Modifier.height(24.dp))
                
                // Details Section
                LazyColumn(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                    item { DetailRow("Profile Name", settings.name) }
                    item { DetailRow("Profile ID", settings.id.toString()) }
                    item { DetailRow("Model Type", settings.modelType.name) }

                    // Type-specific details
                    when (settings) {
                        is ChatModelSettings -> ChatSettingsDetails(settings)
                        is EmbeddingModelSettings -> EmbeddingSettingsDetails(settings)
                        // Add cases for other settings types as they are implemented
                        else -> item { Text("Details for this settings type are not yet implemented.") }
                    }
                }
            }
        }
    }
}

// Helper composables for type-specific details
private fun LazyListScope.ChatSettingsDetails(settings: ChatModelSettings) {
    item { DetailRow("System Message", settings.systemMessage ?: "Not set") }
    item { DetailRow("Temperature", settings.temperature?.toString() ?: "Default") }
    item { DetailRow("Max Tokens", settings.maxTokens?.toString() ?: "Default") }
    item { DetailRow("Top P", settings.topP?.toString() ?: "Default") }
    item { DetailRow("Top K", settings.topK?.toString() ?: "Default") }
    item { DetailRow("Stream", settings.stream.toString()) }
    item { DetailRow("Stop Sequences", settings.stopSequences?.joinToString(", ") ?: "None") }
}

private fun LazyListScope.EmbeddingSettingsDetails(settings: EmbeddingModelSettings) {
    item { DetailRow("Dimensions", settings.dimensions?.toString() ?: "Default") }
    item { DetailRow("Encoding Format", settings.encodingFormat ?: "Default") }
}
```

---

### **Phase 3: Dialogs & Forms for Add/Edit**

This is the most complex phase, involving dynamic forms based on `SettingsFormState`.

**3.1. Implement `SettingsDialogs.kt`:**

This composable acts as a router, displaying the correct dialog based on the `dialogState`.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsDialogs.kt

@Composable
fun SettingsDialogs(
    dialogState: SettingsDialogState,
    actions: SettingsConfigTabActions
) {
    when (dialogState) {
        is SettingsDialogState.AddNewSettings -> {
            SettingsFormDialog(
                title = "Add New Settings Profile",
                formState = dialogState.formState,
                onFormUpdate = actions::onUpdateSettingsForm,
                onSave = actions::onSaveSettings,
                onCancel = actions::onCancelDialog
            )
        }
        is SettingsDialogState.EditSettings -> {
            SettingsFormDialog(
                title = "Edit Settings Profile",
                formState = dialogState.formState,
                onFormUpdate = actions::onUpdateSettingsForm,
                onSave = actions::onSaveSettings,
                onCancel = actions::onCancelDialog
            )
        }
        is SettingsDialogState.DeleteSettings -> {
            DeleteSettingsDialog(
                settings = dialogState.settings,
                onConfirm = { actions.onDeleteSettings(dialogState.settings.id) },
                onDismiss = actions::onCancelDialog
            )
        }
        is SettingsDialogState.None -> { /* No dialog */ }
    }
}

// We will add DeleteSettingsDialog in the next phase
```

**3.2. Implement `SettingsFormDialog.kt`:**

This dialog will contain the dynamic form.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsFormDialog.kt
import eu.torvian.chatbot.app.compose.common.ConfigCheckbox
import eu.torvian.chatbot.app.compose.common.ConfigTextField

@Composable
fun SettingsFormDialog(
    title: String,
    formState: SettingsFormState,
    onFormUpdate: ((SettingsFormState) -> SettingsFormState) -> Unit,
    onSave: () -> Unit,
    onCancel: () -> Unit
) {
    Dialog(onDismissRequest = onCancel) {
        Card(modifier = Modifier.widthIn(min = 500.dp, max = 700.dp)) {
            Column(modifier = Modifier.padding(24.dp)) {
                Text(title, style = MaterialTheme.typography.headlineSmall)
                Spacer(modifier = Modifier.height(24.dp))

                Column(
                    modifier = Modifier.verticalScroll(rememberScrollState()),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Common Fields
                    ConfigTextField(
                        value = formState.name,
                        onValueChange = { value -> onFormUpdate { it.withUpdatedName(value) } },
                        label = "Profile Name *",
                        isError = formState.name.isBlank()
                    )

                    // Dynamic Form Content
                    when (formState) {
                        is SettingsFormState.Chat -> ChatFormContent(formState, onFormUpdate)
                        is SettingsFormState.Embedding -> EmbeddingFormContent(formState, onFormUpdate)
                    }

                    // Custom Params (Advanced)
                    ConfigTextField(
                        value = formState.customParamsJson,
                        onValueChange = { value -> onFormUpdate { it.withUpdatedCustomParams(value) } },
                        label = "Custom Parameters (JSON)",
                        singleLine = false,
                        modifier = Modifier.height(120.dp)
                    )

                    // Error Message
                    formState.errorMessage?.let {
                        Text(it, color = MaterialTheme.colorScheme.error, style = MaterialTheme.typography.bodyMedium)
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))
                // Action Buttons
                Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.End) {
                    TextButton(onClick = onCancel) { Text("Cancel") }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(onClick = onSave, enabled = formState.name.isNotBlank()) {
                        Text(if (formState.mode == FormMode.NEW) "Add Profile" else "Save Changes")
                    }
                }
            }
        }
    }
}

// Add helper functions to update sealed class state
private fun SettingsFormState.withUpdatedName(name: String): SettingsFormState {
    return when(this) {
        is SettingsFormState.Chat -> copy(name = name)
        is SettingsFormState.Embedding -> copy(name = name)
    }
}
private fun SettingsFormState.withUpdatedCustomParams(json: String): SettingsFormState {
     return when(this) {
        is SettingsFormState.Chat -> copy(customParamsJson = json)
        is SettingsFormState.Embedding -> copy(customParamsJson = json)
    }
}


@Composable
private fun ChatFormContent(
    formState: SettingsFormState.Chat,
    onFormUpdate: ((SettingsFormState) -> SettingsFormState) -> Unit
) {
    ConfigTextField(
        value = formState.systemMessage,
        onValueChange = { onFormUpdate { (it as SettingsFormState.Chat).copy(systemMessage = it) } },
        label = "System Message",
        singleLine = false,
        modifier = Modifier.height(120.dp)
    )
    Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
        ConfigTextField(
            value = formState.temperature,
            onValueChange = { onFormUpdate { (it as SettingsFormState.Chat).copy(temperature = it) } },
            label = "Temperature",
            modifier = Modifier.weight(1f)
        )
        ConfigTextField(
            value = formState.maxTokens,
            onValueChange = { onFormUpdate { (it as SettingsFormState.Chat).copy(maxTokens = it) } },
            label = "Max Tokens",
            modifier = Modifier.weight(1f)
        )
    }
    Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
        ConfigTextField(
            value = formState.topP,
            onValueChange = { onFormUpdate { (it as SettingsFormState.Chat).copy(topP = it) } },
            label = "Top P",
            modifier = Modifier.weight(1f)
        )
        ConfigTextField(
            value = formState.topK,
            onValueChange = { onFormUpdate { (it as SettingsFormState.Chat).copy(topK = it) } },
            label = "Top K",
            modifier = Modifier.weight(1f)
        )
    }
    ConfigTextField(
        value = formState.stopSequences,
        onValueChange = { onFormUpdate { (it as SettingsFormState.Chat).copy(stopSequences = it) } },
        label = "Stop Sequences (comma-separated)"
    )
    ConfigCheckbox(
        checked = formState.stream,
        onCheckedChange = { onFormUpdate { (it as SettingsFormState.Chat).copy(stream = it) } },
        label = "Enable Streaming"
    )
}

@Composable
private fun EmbeddingFormContent(
    formState: SettingsFormState.Embedding,
    onFormUpdate: ((SettingsFormState) -> SettingsFormState) -> Unit
) {
    ConfigTextField(
        value = formState.dimensions,
        onValueChange = { onFormUpdate { (it as SettingsFormState.Embedding).copy(dimensions = it) } },
        label = "Dimensions"
    )
    ConfigTextField(
        value = formState.encodingFormat,
        onValueChange = { onFormUpdate { (it as SettingsFormState.Embedding).copy(encodingFormat = it) } },
        label = "Encoding Format"
    )
}
```

---

### **Phase 4: Implementing Delete Functionality**

Now, we'll add the confirmation dialog for deletions.

**4.1. Create `DeleteSettingsDialog`:**

Add this composable inside the `SettingsDialogs.kt` file.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsDialogs.kt

@Composable
private fun DeleteSettingsDialog(
    settings: ModelSettings,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Delete Settings Profile") },
        text = {
            Text("Are you sure you want to delete the profile '${settings.name}'? This action cannot be undone.")
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) {
                Text("Delete")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
```
The `SettingsDialogs` router from Phase 3.1 already handles showing this dialog. No further changes are needed there.

---

### **Phase 5: Final Integration and Refinement**

The final step is to wire everything up in `SettingsScreen.kt`.

**5.1. Update `SettingsScreen.kt`:**

Implement the `SettingsConfigTabActions` and pass them to the `SettingsConfigTab`.

```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/settings/SettingsScreen.kt

// ... inside SettingsScreen Composable ...

    // (SettingsConfigTabState creation is already correct)
    
    // Create action implementations
    // ... (providersTabActions and modelsTabActions are already there) ...
    val settingsConfigTabActions = object : SettingsConfigTabActions {
        override fun onLoadModelsAndSettings() = settingsConfigViewModel.loadModelsAndSettings()
        override fun onSelectModel(model: LLMModel?) = settingsConfigViewModel.selectModel(model)
        override fun onSelectSettings(settings: ModelSettings?) = settingsConfigViewModel.selectSettings(settings)
        override fun onStartAddingNewSettings() = settingsConfigViewModel.startAddingNewSettings()
        override fun onStartEditingSettings(settings: ModelSettings) = settingsConfigViewModel.startEditingSettings(settings)
        override fun onStartDeletingSettings(settings: ModelSettings) = settingsConfigViewModel.startDeletingSettings(settings)
        override fun onUpdateSettingsForm(update: (SettingsFormState) -> SettingsFormState) = settingsConfigViewModel.updateSettingsForm(update)
        override fun onSaveSettings() = settingsConfigViewModel.saveSettings()
        override fun onDeleteSettings(settingsId: Long) = settingsConfigViewModel.deleteSettings(settingsId)
        override fun onCancelDialog() = settingsConfigViewModel.cancelDialog()
    }

    Column(...) {
        // ... (TabRow is already there) ...
        Box(...) {
            when (selectedTabIndex) {
                0 -> { /* ... */ }
                1 -> { /* ... */ }
                2 -> SettingsConfigTab(
                    state = settingsConfigTabState,
                    actions = settingsConfigTabActions
                )
            }
        }
    }
}
```

---

### **Code Review & Final Polish Checklist**

-   [ ] **State Handling:** Ensure all `DataState` branches (`Loading`, `Error`, `Success`, `Idle`) are handled gracefully in `SettingsConfigTab`.
-   [ ] **Form Updates:** Verify that `onValueChange` in all form fields correctly calls `actions.onUpdateSettingsForm` with the appropriate `copy()` on the sealed class instance.
-   [ ] **Empty States:** Check that empty states (no models, no settings for a model) are displayed clearly.
-   [ ] **Responsiveness:** Ensure the UI components resize reasonably within the master-detail layout.
-   [ ] **Consistency:** The new UI should visually match the existing `ProvidersTab` and `ModelsTab` (e.g., card styles, typography, button placement).
-   [ ] **Error Display:** Confirm that validation errors from `formState.errorMessage` are visible in the form dialog.
-   [ ] **Button States:** Ensure the "Save" button in the form is disabled when the required fields (like `name`) are empty.

This comprehensive plan provides a clear path to implementing the required UI, ensuring all features of the `SettingsConfigViewModel` are represented and the code remains organized and maintainable.