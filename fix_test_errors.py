#!/usr/bin/env python3
"""
Script to fix the remaining Ktor API client test files by:
1. Adding missing ApiResourceError import
2. Fixing error property access patterns
"""

import os
import re

# Files to fix
test_files = [
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorModelApiClientTest.kt",
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorProviderApiClientTest.kt", 
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorSessionApiClientTest.kt",
    "app/src/desktopTest/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorSettingsApiClientTest.kt"
]

def fix_file(filepath):
    """Fix a single test file"""
    print(f"Fixing {filepath}...")

    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()

    # Add ApiResourceError import if not present
    if "import eu.torvian.chatbot.app.service.api.ApiResourceError" not in content:
        content = content.replace(
            "import arrow.core.Either\n",
            "import arrow.core.Either\nimport eu.torvian.chatbot.app.service.api.ApiResourceError\n"
        )

    # Fix SerializationException tests - these should expect SerializationError, not ServerError
    serialization_pattern = re.compile(
        r'(\s+)val error = result\.value as ApiResourceError\.ServerError\s*\n'
        r'(\s+)assertEquals\(500, error\.apiError\.statusCode\)\s*\n'
        r'(\s+)assertEquals\(CommonApiErrorCodes\.INTERNAL\.code, error\.apiError\.code\)\s*\n'
        r'(\s+)assertTrue\(error\.apiError\.message\.contains\("Data Serialization/Deserialization Error"\)\)',
        re.MULTILINE
    )

    def replace_serialization_test(match):
        indent = match.group(1)
        return (f'{indent}val error = result.value as ApiResourceError.SerializationError\n'
                f'{indent}assertTrue(error.message.contains("Serialization Error"))\n'
                f'{indent}assertTrue(error.description.contains("Failed to parse API response"))')

    content = serialization_pattern.sub(replace_serialization_test, content)

    # Fix regular server error tests - these should remain as ServerError
    # Pattern 1: val error = result.value -> val error = result.value as ApiResourceError.ServerError
    # (but only if not already fixed by serialization pattern)
    content = re.sub(
        r'(\s+)val error = result\.value\s*\n(?!\s+assertTrue)',
        r'\1val error = result.value as ApiResourceError.ServerError\n',
        content
    )

    # Pattern 2: error.statusCode -> error.apiError.statusCode
    content = re.sub(r'error\.statusCode', 'error.apiError.statusCode', content)

    # Pattern 3: error.code -> error.apiError.code
    content = re.sub(r'error\.code', 'error.apiError.code', content)

    # Pattern 4: error.message -> error.apiError.message (but not in SerializationError tests)
    content = re.sub(r'(?<!SerializationError\)\s*\n\s+assertTrue\()error\.message', 'error.apiError.message', content)

    # Pattern 5: error.details -> error.apiError.details
    content = re.sub(r'error\.details', 'error.apiError.details', content)

    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Fixed {filepath}")

def main():
    """Main function"""
    for filepath in test_files:
        if os.path.exists(filepath):
            fix_file(filepath)
        else:
            print(f"File not found: {filepath}")
    
    print("All files fixed!")

if __name__ == "__main__":
    main()
